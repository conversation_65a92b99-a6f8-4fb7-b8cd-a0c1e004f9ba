const { chromium } = require('playwright');

async function useMyChrome() {
  try {
    console.log('正在启动使用你的Chrome数据的浏览器...');
    console.log('注意：请先关闭所有Chrome浏览器窗口！');
    
    // 使用你的Chrome用户数据目录
    const context = await chromium.launchPersistentContext(
      'C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data',
      {
        headless: false,
        channel: 'chrome',
        viewport: { width: 1920, height: 1080 },
        // 添加一些选项来避免冲突
        args: [
          '--disable-web-security',
          '--disable-features=VizDisplayCompositor'
        ]
      }
    );
    
    const page = context.pages()[0] || await context.newPage();
    
    // 测试访问GitHub，看看是否已经登录
    await page.goto('https://github.com');
    console.log('成功打开GitHub！');
    
    // 等待页面加载
    await page.waitForTimeout(3000);
    
    // 检查是否已经登录
    try {
      const signInButton = await page.locator('a[href="/login"]').first();
      const isVisible = await signInButton.isVisible();
      
      if (isVisible) {
        console.log('❌ 还没有登录状态，需要手动登录一次');
      } else {
        console.log('✅ 太好了！你已经是登录状态了！');
      }
    } catch (e) {
      console.log('正在检查登录状态...');
    }
    
    console.log('浏览器将保持打开状态，你可以查看和操作');
    console.log('按 Ctrl+C 来关闭');
    
    // 保持运行
    await new Promise(() => {});
    
  } catch (error) {
    console.error('错误:', error.message);
    
    if (error.message.includes('running') || error.message.includes('lock')) {
      console.log('\n解决方案：');
      console.log('1. 关闭所有Chrome浏览器窗口');
      console.log('2. 等待几秒钟');
      console.log('3. 重新运行这个脚本');
    }
  }
}

useMyChrome();
